import discord
from discord.ext import commands
import logging
import asyncio
import threading
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timezone
import queue

logger = logging.getLogger('discord')

class DiscordHandler:
    def __init__(self, token: str, admin_id: str, guild_id: str, channel_name: str = "mexc-auto"):
        self.token = token
        self.admin_id = int(admin_id)
        self.guild_id = int(guild_id)
        self.channel_name = channel_name
        self.command_handlers = {}
        self.message_queue = queue.Queue()
        self.stop_bot = False
        self.bot_thread = None
        self.channel = None
        
        intents = discord.Intents.default()
        intents.message_content = True
        self.bot = commands.Bot(command_prefix='/', intents=intents)
        
        self.register_command('start', self._handle_start)
        self.register_command('help', self._handle_help)
        self.register_command('keyboard', self._handle_keyboard)
        
        self._setup_bot_events()
        logger.info("DiscordHandler đã được khởi tạo")

    def _setup_bot_events(self):
        @self.bot.event
        async def on_ready():
            logger.info(f'Discord bot đã kết nối: {self.bot.user}')
            guild = self.bot.get_guild(self.guild_id)
            if guild:
                self.channel = discord.utils.get(guild.channels, name=self.channel_name)
                if self.channel:
                    logger.info(f'Đã tìm thấy channel: {self.channel_name}')
                else:
                    logger.error(f'Không tìm thấy channel: {self.channel_name}')
            else:
                logger.error(f'Không tìm thấy guild với ID: {self.guild_id}')

        @self.bot.event
        async def on_message(message):
            if message.author == self.bot.user:
                return
            
            if message.author.id != self.admin_id:
                logger.warning(f"Tin nhắn từ người dùng không được phép: {message.author.id}")
                return
            
            if message.content.startswith('/'):
                await self._process_command(message)

    async def _process_command(self, message):
        content = message.content[1:]
        command_parts = content.split(' ', 1)
        command = command_parts[0].lower()
        args = command_parts[1] if len(command_parts) > 1 else ""
        
        if command in self.command_handlers:
            try:
                result = self.command_handlers[command](str(message.author.id), args, message)
                if result:
                    await self.send_message(result)
            except Exception as e:
                logger.error(f"Lỗi khi xử lý lệnh {command}: {e}")
                await self.send_message(f"⚠️ Lỗi xử lý lệnh: {str(e)}")
        else:
            await self.send_message(f"❓ Lệnh không được hỗ trợ: {command}")

    async def send_message(self, message: str) -> bool:
        if not self.channel:
            logger.warning("Channel chưa được thiết lập")
            return False
        
        if len(message) > 2000:
            message = message[:1997] + "..."
        
        try:
            await self.channel.send(message)
            return True
        except Exception as e:
            logger.error(f"Lỗi gửi tin nhắn Discord: {e}")
            return False

    def send_message_sync(self, message: str) -> bool:
        if not self.bot.is_ready():
            logger.warning("Bot chưa sẵn sàng")
            return False
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self.send_message(message))
        except Exception as e:
            logger.error(f"Lỗi gửi tin nhắn sync: {e}")
            return False
        finally:
            loop.close()

    def start_polling(self):
        if self.bot_thread and self.bot_thread.is_alive():
            logger.warning("Discord bot đã đang chạy")
            return
        
        self.stop_bot = False
        self.bot_thread = threading.Thread(target=self._run_bot, daemon=True)
        self.bot_thread.start()
        logger.info("Đã bắt đầu Discord bot")

    def _run_bot(self):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.bot.start(self.token))
        except Exception as e:
            logger.error(f"Lỗi chạy Discord bot: {e}")
        finally:
            loop.close()

    def stop_polling_updates(self):
        self.stop_bot = True
        if self.bot_thread:
            asyncio.run_coroutine_threadsafe(self.bot.close(), self.bot.loop)
            self.bot_thread.join(timeout=5)
            logger.info("Đã dừng Discord bot")

    def register_command(self, command: str, handler: Callable):
        self.command_handlers[command] = handler
        logger.debug(f"Đã đăng ký lệnh: {command}")

    def _handle_start(self, user_id: str, args: str, message) -> str:
        return "🤖 **Trading Bot Discord**\n\nBot đã sẵn sàng hoạt động!\nSử dụng /help để xem danh sách lệnh."

    def _handle_help(self, user_id: str, args: str, message) -> str:
        help_text = """
🤖 **DANH SÁCH LỆNH BOT**

**📊 Thông tin:**
/status - Trạng thái bot
/balance - Số dư tài khoản  
/stats - Thống kê giao dịch
/orders - Lệnh đang mở
/watchlist - Danh sách theo dõi
/openpositions - Vị thế đang mở

**⚙️ Cấu hình:**
/settings - Cài đặt bot
/clearsymbol - Xóa symbol khỏi danh sách loại trừ

**🎮 Điều khiển:**
/pause - Tạm dừng bot
/resume - Tiếp tục bot
/updatesymbols [số lượng] - Cập nhật symbols

**❓ Hỗ trợ:**
/help - Hiển thị menu này
"""
        return help_text

    def _handle_keyboard(self, user_id: str, args: str, message) -> str:
        return "📱 **Menu lệnh nhanh:**\n\n" + self._handle_help(user_id, args, message)

    def send_main_keyboard(self):
        keyboard_message = """
🎮 **MENU LỆNH NHANH**

Sử dụng các lệnh sau:
• /status - Xem trạng thái
• /balance - Kiểm tra số dư
• /orders - Lệnh đang mở
• /pause - Tạm dừng bot
• /resume - Tiếp tục bot
"""
        if self.bot.is_ready():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self.send_message(keyboard_message))
            except Exception as e:
                logger.error(f"Lỗi gửi keyboard message: {e}")
            finally:
                loop.close()

    def register_trading_commands(self, callbacks):
        if 'status' in callbacks:
            self.register_command('status',
                lambda user_id, args, msg: self.handle_status(user_id, args, msg, callbacks['status']))

        if 'balance' in callbacks:
            self.register_command('balance',
                lambda user_id, args, msg: self.handle_balance(user_id, args, msg, callbacks['balance']))

        if 'stats' in callbacks:
            self.register_command('stats',
                lambda user_id, args, msg: self.handle_stats(user_id, args, msg, callbacks['stats']))

        if 'orders' in callbacks:
            self.register_command('orders',
                lambda user_id, args, msg: self.handle_orders(user_id, args, msg, callbacks['orders']))

        if 'watchlist' in callbacks:
            self.register_command('watchlist',
                lambda user_id, args, msg: self.handle_watchlist(user_id, args, msg, callbacks['watchlist']))

        if 'openpositions' in callbacks:
            self.register_command('openpositions',
                lambda user_id, args, msg: self.handle_openpositions(user_id, args, msg, callbacks['openpositions']))

        if 'settings' in callbacks:
            self.register_command('settings',
                lambda user_id, args, msg: self.handle_settings(user_id, args, msg, callbacks['settings']))

        if 'clearsymbol' in callbacks:
            self.register_command('clearsymbol',
                lambda user_id, args, msg: self.handle_clearsymbol(user_id, args, msg, callbacks['clearsymbol']))

        if 'pause' in callbacks:
            self.register_command('pause',
                lambda user_id, args, msg: self.handle_pause(user_id, args, msg, callbacks['pause']))

        if 'resume' in callbacks:
            self.register_command('resume',
                lambda user_id, args, msg: self.handle_resume(user_id, args, msg, callbacks['resume']))

    def handle_status(self, user_id: str, args: str, message, callback) -> str:
        try:
            return callback()
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh status: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_balance(self, user_id: str, args: str, message, callback) -> str:
        try:
            return callback()
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh balance: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_stats(self, user_id: str, args: str, message, callback) -> str:
        try:
            return callback()
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh stats: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_orders(self, user_id: str, args: str, message, callback) -> str:
        try:
            return callback()
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh orders: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_watchlist(self, user_id: str, args: str, message, callback) -> str:
        try:
            return callback()
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh watchlist: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_openpositions(self, user_id: str, args: str, message, callback) -> str:
        try:
            return callback()
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh openpositions: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_settings(self, user_id: str, args: str, message, callback) -> str:
        try:
            return callback(args)
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh settings: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_clearsymbol(self, user_id: str, args: str, message, callback) -> str:
        try:
            return callback(args)
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh clearsymbol: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_pause(self, user_id: str, args: str, message, callback) -> str:
        try:
            return callback()
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh pause: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_resume(self, user_id: str, args: str, message, callback) -> str:
        try:
            return callback()
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh resume: {e}")
            return f"⚠️ Lỗi: {str(e)}"

    def handle_update_symbols(self, user_id: str, args: str, message, callback) -> str:
        try:
            count = 20
            if args.strip():
                try:
                    count = int(args.strip())
                except ValueError:
                    return "⚠️ Số lượng symbol không hợp lệ. Sử dụng: /updatesymbols [số]"
            return callback(count)
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh updatesymbols: {e}")
            return f"⚠️ Lỗi: {str(e)}"
